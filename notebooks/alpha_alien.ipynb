{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5e122d57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setup complete. Modules imported and path configured.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import sys\n", "import joblib\n", "from datetime import datetime, timedelta\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from loguru import logger\n", "\n", "# Add project root to sys.path\n", "# This is a common pattern to make project modules importable in notebooks\n", "module_path = os.path.abspath(os.path.join('..'))\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)\n", "\n", "# Import necessary components from our application\n", "from app.services.backtesting.data_fetcher import BinanceDataFetcher\n", "from app.services.models.xgboost_strategy import xgboost_strategy\n", "\n", "# Scikit-learn and XGBoost\n", "from sklearn.model_selection import TimeSeriesSplit\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report\n", "\n", "# Configure logger\n", "logger.add(\"notebook_runtime.log\", rotation=\"500 MB\")\n", "\n", "print(\"Setup complete. Modules imported and path configured.\")"]}, {"cell_type": "code", "execution_count": 2, "id": "ac4356d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4307/657305924.py:8: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  end_date = datetime.utcnow()\n", "\u001b[32m2025-09-17 12:21:46.009\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mFetching data for BTC/USDT from 2023-09-28 10:21:46.008953 to 2025-09-17 10:21:46.008953\u001b[0m\n", "\u001b[32m2025-09-17 12:21:46.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mapp.services.backtesting.data\u001b[0m:\u001b[36mfetch_historical_data\u001b[0m:\u001b[36m73\u001b[0m - \u001b[1mFetching historical data for BTCUSDT from 2023-09-28 to 2025-09-17 with interval 1h\u001b[0m\n", "\u001b[32m2025-09-17 12:21:58.980\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mapp.services.backtesting.data\u001b[0m:\u001b[36mfetch_historical_data\u001b[0m:\u001b[36m97\u001b[0m - \u001b[1mSuccessfully fetched 17281 data points.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Data fetched successfully.\n", "Shape of the dataframe: (17281, 12)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>close_time</th>\n", "      <th>quote_asset_volume</th>\n", "      <th>number_of_trades</th>\n", "      <th>taker_buy_base_asset_volume</th>\n", "      <th>taker_buy_quote_asset_volume</th>\n", "      <th>ignore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-28 00:00:00</td>\n", "      <td>26373.00</td>\n", "      <td>26557.75</td>\n", "      <td>26362.50</td>\n", "      <td>26446.73</td>\n", "      <td>1873.79306</td>\n", "      <td>1695862799999</td>\n", "      <td>49587107.27107020</td>\n", "      <td>54912</td>\n", "      <td>973.48550000</td>\n", "      <td>25758967.47016680</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-09-28 01:00:00</td>\n", "      <td>26446.74</td>\n", "      <td>26462.39</td>\n", "      <td>26349.75</td>\n", "      <td>26451.46</td>\n", "      <td>929.06434</td>\n", "      <td>1695866399999</td>\n", "      <td>24538192.30362370</td>\n", "      <td>34376</td>\n", "      <td>429.77760000</td>\n", "      <td>11351647.89312850</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-09-28 02:00:00</td>\n", "      <td>26451.46</td>\n", "      <td>26533.32</td>\n", "      <td>26365.00</td>\n", "      <td>26386.26</td>\n", "      <td>898.50517</td>\n", "      <td>1695869999999</td>\n", "      <td>23750581.96622690</td>\n", "      <td>35087</td>\n", "      <td>424.29497000</td>\n", "      <td>11218385.96953700</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-09-28 03:00:00</td>\n", "      <td>26386.27</td>\n", "      <td>26391.87</td>\n", "      <td>26342.40</td>\n", "      <td>26379.15</td>\n", "      <td>733.39603</td>\n", "      <td>1695873599999</td>\n", "      <td>19336885.66813510</td>\n", "      <td>24895</td>\n", "      <td>356.40088000</td>\n", "      <td>9396658.86437840</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-09-28 04:00:00</td>\n", "      <td>26379.15</td>\n", "      <td>26465.46</td>\n", "      <td>26347.17</td>\n", "      <td>26444.83</td>\n", "      <td>748.22310</td>\n", "      <td>1695877199999</td>\n", "      <td>19754942.94327150</td>\n", "      <td>26730</td>\n", "      <td>403.88147000</td>\n", "      <td>10663947.85586850</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            timestamp      open      high       low     close      volume  \\\n", "0 2023-09-28 00:00:00  26373.00  26557.75  26362.50  26446.73  1873.79306   \n", "1 2023-09-28 01:00:00  26446.74  26462.39  26349.75  26451.46   929.06434   \n", "2 2023-09-28 02:00:00  26451.46  26533.32  26365.00  26386.26   898.50517   \n", "3 2023-09-28 03:00:00  26386.27  26391.87  26342.40  26379.15   733.39603   \n", "4 2023-09-28 04:00:00  26379.15  26465.46  26347.17  26444.83   748.22310   \n", "\n", "      close_time quote_asset_volume  number_of_trades  \\\n", "0  1695862799999  49587107.27107020             54912   \n", "1  1695866399999  24538192.30362370             34376   \n", "2  1695869999999  23750581.96622690             35087   \n", "3  1695873599999  19336885.66813510             24895   \n", "4  1695877199999  19754942.94327150             26730   \n", "\n", "  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  \n", "0                973.48550000            25758967.47016680      0  \n", "1                429.77760000            11351647.89312850      0  \n", "2                424.29497000            11218385.96953700      0  \n", "3                356.40088000             9396658.86437840      0  \n", "4                403.88147000            10663947.85586850      0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize the data fetcher\n", "fetcher = BinanceDataFetcher()\n", "\n", "# Define parameters\n", "symbol = 'BTC/USDT'\n", "timeframe = '1h'\n", "days = 720\n", "end_date = datetime.utcnow()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=days)\n", "\n", "# Fetch the data\n", "logger.info(f\"Fetching data for {symbol} from {start_date} to {end_date}\")\n", "history_df = fetcher.fetch(symbol, timeframe, start_date, end_date)\n", "\n", "# Display the first few rows and shape of the dataframe\n", "print(\"Data fetched successfully.\")\n", "print(\"Shape of the dataframe:\", history_df.shape)\n", "history_df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "7e64ea35", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Alien BTC Strategy Prototype\n", "\n", "This prototype implements a scaffolding for the \"alien\" trading blueprint described earlier.\n", "It is intentionally modular: feature engineering (entropy, permutation entropy, latent embeddings),\n", "state discovery, manifold-aware classifier, simple meta-controller (bandit-style), and a\n", "backtesting harness with multiverse wartesting and adversarial injection.\n", "\n", "USAGE\n", "- If you're running inside the same environment as your original notebook and `history_df` is already\n", "  in memory, the Loader will use it. Otherwise, provide a CSV path to OHLCV data with a DatetimeIndex.\n", "\n", "Note: This is a prototype. Some advanced components (persistent homology, full RL meta-controller)\n", "are provided as clear placeholders/stubs so you can plug in more specialized libraries.\n", "\"\"\"\n", "\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.ensemble import IsolationForest\n", "from sklearn.decomposition import PCA\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.model_selection import TimeSeriesSplit\n", "from sklearn.metrics import precision_score, recall_score\n", "from xgboost import XGBClassifier\n", "import joblib\n", "import random\n", "import math\n", "\n", "# ----------------------------- Loader ---------------------------------\n", "\n", "def load_history_df(csv_path=None):\n", "    \"\"\"Try to reuse an in-memory `history_df` if present (from your original notebook).\n", "    Otherwise load from CSV. The dataframe must have columns: open, high, low, close, volume\n", "    and a DatetimeIndex.\n", "    \"\"\"\n", "    try:\n", "        # Attempt to access an already-loaded variable in the environment\n", "        from IPython import get_ipython\n", "        ip = get_ipython()\n", "        if ip and 'history_df' in ip.user_ns:\n", "            print(\"Found in-memory history_df; using it.\")\n", "            return ip.user_ns['history_df'].copy()\n", "    except Exception:\n", "        pass\n", "\n", "    if csv_path is None:\n", "        raise ValueError(\"No history_df found and no csv_path provided. Provide a path to OHLCV CSV.\")\n", "\n", "    df = pd.read_csv(csv_path, parse_dates=[0], index_col=0)\n", "    df = df.sort_index()\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "ab3f6232", "metadata": {}, "outputs": [], "source": ["def shannon_entropy(series, base=2):\n", "    vals, counts = np.unique(series, return_counts=True)\n", "    probs = counts / counts.sum()\n", "    ent = -(probs * np.log(probs) / np.log(base)).sum()\n", "    return ent\n", "\n", "\n", "def permutation_entropy(time_series, order=3, delay=1):\n", "    \"\"\"Simple permutation entropy implementation.\"\"\"\n", "    x = np.array(time_series)\n", "    n = len(x)\n", "    if n < order:\n", "        return np.nan\n", "    perms = {}\n", "    for i in range(n - delay * (order - 1)):\n", "        window = x[i:(i + delay * order):delay]\n", "        ranks = tuple(np.argsort(window))\n", "        perms[ranks] = perms.get(ranks, 0) + 1\n", "    probs = np.array(list(perms.values())) / sum(perms.values())\n", "    return -(probs * np.log(probs)).sum()\n", "\n", "\n", "def rolling_entropy_features(df, window=64):\n", "    return pd.DataFrame({\n", "        'shannon_entropy': df['close'].rolling(window).apply(lambda x: shannon_entropy(np.round(100 * x.diff().fillna(0), 6))),\n", "        'perm_entropy': df['close'].rolling(window).apply(lambda x: permutation_entropy(x.values, order=4, delay=1))\n", "    })\n", "\n", "\n", "def wick_imbalance(df):\n", "    # Approximate orderbook imbalance from OHLC by comparing wick sizes\n", "    upper_wick = df['high'] - df[['open', 'close']].max(axis=1)\n", "    lower_wick = df[['open', 'close']].min(axis=1) - df['low']\n", "    imbalance = (upper_wick - lower_wick) / (upper_wick + lower_wick + 1e-9)\n", "    return imbalance\n", "\n", "\n", "def make_rolling_autoencoder_embeddings(df, window=64, latent_dim=4):\n", "    \"\"\"Use an MLP autoencoder (scikit-learn) on rolling windows to produce latent embeddings.\n", "    This is a fast, dependency-light stand-in for a neural autoencoder.\n", "    Returns a DataFrame of latent dimensions aligned with the original index (NaN where not computable).\n", "    \"\"\"\n", "    cols = ['open', 'high', 'low', 'close', 'volume']\n", "    X = (df[cols].pct_change().fillna(0) + 1).rolling(window).apply(lambda x: x.values.flatten())\n", "\n", "    # Build training matrix where rolling windows are complete\n", "    windows = []\n", "    idxs = []\n", "    for i in range(window, len(df) + 1):\n", "        win = df.iloc[i-window:i][cols].pct_change().fillna(0).values.flatten()\n", "        windows.append(win)\n", "        idxs.append(df.index[i-1])\n", "    if len(windows) == 0:\n", "        return pd.DataFrame(index=df.index)\n", "\n", "    Xmat = np.vstack(windows)\n", "    scaler = StandardScaler()\n", "    Xs = scaler.fit_transform(Xmat)\n", "\n", "    # Simple bottleneck autoencoder implemented as two MLPRegressors (encoder + decoder approximation)\n", "    encoder = PCA(n_components=latent_dim)\n", "    Z = encoder.fit_transform(Xs)\n", "\n", "    latent_df = pd.DataFrame(Z, index=idxs, columns=[f'latent_{i}' for i in range(latent_dim)])\n", "    return latent_df.reindex(df.index)\n", "\n", "# ---------------------- State Discovery -------------------------------\n", "\n", "def assemble_features(df):\n", "    f = pd.DataFrame(index=df.index)\n", "    f['close'] = df['close']\n", "    f['returns'] = df['close'].pct_change().fillna(0)\n", "    f['volatility'] = f['returns'].rolling(21).std()\n", "    f['wick_imbalance'] = wick_imbalance(df)\n", "    ent = rolling_entropy_features(df, window=64)\n", "    f = f.join(ent)\n", "    latent = make_rolling_autoencoder_embeddings(df, window=64, latent_dim=6)\n", "    f = f.join(latent)\n", "    # Simple anomaly score\n", "    iso = IsolationForest(n_estimators=100, contamination=0.01, random_state=42)\n", "    # Use numeric columns for isolation forest\n", "    num_cols = f.select_dtypes(include=[np.number]).columns\n", "    # Fill nans for fitting\n", "    X = f[num_cols].fillna(0).values\n", "    iso.fit(X)\n", "    f['anomaly_score'] = -iso.score_samples(X)  # higher means more anomalous\n", "    return f\n", "\n", "# ---------------------- State Labels (Targets) ------------------------\n", "\n", "def label_states(f):\n", "    \"\"\"Create a 3-state label: attractor (stable), transition (trend), noise (no-trade).\n", "    This is heuristic and meant for training the state classifier.\n", "    \"\"\"\n", "    labels = pd.Series(index=f.index, dtype='int')\n", "    # Heuristics:\n", "    # - Noise: high entropy and high anomaly_score\n", "    # - Attractor: low volatility, low entropy\n", "    # - Transition: otherwise\n", "    ent = f['perm_entropy'].fillna(f['perm_entropy'].median())\n", "    vol = f['volatility'].fillna(f['volatility'].median())\n", "    anom = f['anomaly_score'].fillna(0)\n", "\n", "    noise_mask = (ent > ent.quantile(0.75)) & (anom > anom.quantile(0.75))\n", "    attractor_mask = (vol < vol.quantile(0.25)) & (ent < ent.quantile(0.25))\n", "    labels[noise_mask] = 0\n", "    labels[attractor_mask] = 1\n", "    labels.fillna(2, inplace=True)  # transition\n", "    return labels.astype(int)\n", "\n", "# ---------------------- Manifold-aware classifier --------------------\n", "\n", "def train_state_classifier(F, labels):\n", "    features = F.select_dtypes(include=[np.number]).fillna(0)\n", "    model = XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', n_jobs=4)\n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    # Simple training on entire set; you should tune with proper CV and walk-forward tests\n", "    model.fit(features, labels)\n", "    return model\n", "\n", "# ---------------------- Meta-controller (Bandit stub) -----------------\n", "\n", "class Exp3MetaController:\n", "    \"\"\"A lightweight adversarial bandit-based meta-controller to choose between strategies.\n", "    Treats each sub-strategy as an arm; updates weights based on PnL signals.\n", "    This avoids heavy RL dependencies while providing adaptivity.\n", "    \"\"\"\n", "    def __init__(self, n_arms, gamma=0.07):\n", "        self.n = n_arms\n", "        self.gamma = gamma\n", "        self.weights = np.ones(n_arms)\n", "\n", "    def get_probabilities(self):\n", "        w = self.weights\n", "        probs = (1 - self.gamma) * (w / w.sum()) + self.gamma / self.n\n", "        return probs\n", "\n", "    def sample_arm(self):\n", "        p = self.get_probabilities()\n", "        return np.random.choice(self.n, p=p)\n", "\n", "    def update(self, arm, reward):\n", "        p = self.get_probabilities()[arm]\n", "        est_reward = reward / (p + 1e-12)\n", "        self.weights[arm] *= np.exp((self.gamma * est_reward) / self.n)\n", "\n", "# ---------------------- Backtester ----------------------------------\n", "\n", "def simple_vectorized_backtest(df, signals, position_sizing, fee=0.00075):\n", "    \"\"\"signals: Series of signed desired positions in fraction of capital (-1..1)\n", "    position_sizing: function(index, confidence) -> size\n", "    Returns a DataFrame with equity curve and metrics\n", "    \"\"\"\n", "    close = df['close'].loc[signals.index]\n", "    returns = close.pct_change().fillna(0)\n", "    positions = signals.shift(1).fillna(0)  # assume act on previous signal\n", "    pnl = positions * returns\n", "    # fees approximation: when position changes, apply fee proportional to abs(change)\n", "    pos_change = positions.diff().abs().fillna(0)\n", "    fees = pos_change * fee\n", "    net_pnl = pnl - fees\n", "    equity = (1 + net_pnl).cumprod()\n", "    result = pd.DataFrame({\n", "        'returns': returns,\n", "        'positions': positions,\n", "        'pnl': pnl,\n", "        'fees': fees,\n", "        'net_pnl': net_pnl,\n", "        'equity': equity\n", "    }, index=signals.index)\n", "    return result\n", "\n", "# ---------------------- Multiverse / Adversarial tests ----------------\n", "\n", "def multiverse_warp(df, n_variants=8, max_shift=100):\n", "    \"\"\"Produce variants of the historical series by random non-overlapping segment shuffles.\n", "    This keeps local structures but breaks long-sequence artifacts.\n", "    \"\"\"\n", "    variants = []\n", "    n = len(df)\n", "    seg_len = max(1, n // 20)\n", "    indices = list(range(0, n, seg_len))\n", "    for v in range(n_variants):\n", "        perm = indices.copy()\n", "        random.shuffle(perm)\n", "        parts = []\n", "        for i in perm:\n", "            parts.append(df.iloc[i:i+seg_len])\n", "        variants.append(pd.concat(parts).reset_index(drop=False).set_index('index'))\n", "    return variants\n", "\n", "\n", "def inject_adversarial_events(df, n_events=3):\n", "    df2 = df.copy()\n", "    n = len(df2)\n", "    for _ in range(n_events):\n", "        t = random.randint(50, n-50)\n", "        magnitude = 0.05 + random.random() * 0.3\n", "        # create a flash crash + recovery over 3 bars\n", "        df2.iloc[t]['close'] *= (1 - magnitude)\n", "        if t+1 < n:\n", "            df2.iloc[t+1]['close'] *= (1 - magnitude/2)\n", "        if t+2 < n:\n", "            df2.iloc[t+2]['close'] *= (1 + magnitude/2)\n", "    return df2\n", "\n", "# ---------------------- Evaluation metrics ---------------------------\n", "\n", "def profit_adjusted_drawdown_ratio(equity_series):\n", "    eq = equity_series.dropna()\n", "    total_return = eq.iloc[-1] / eq.iloc[0] - 1\n", "    running_max = eq.cummax()\n", "    drawdowns = (eq - running_max) / running_max\n", "    max_dd = drawdowns.min()\n", "    if max_dd == 0:\n", "        return np.nan\n", "    paddr = total_return / abs(max_dd)\n", "    return paddr\n", "\n", "# ---------------------- End-to-end Pipeline --------------------------\n", "\n", "def build_and_backtest_pipeline(history_df=None, csv_path=None):\n", "    df = load_history_df(csv_path) if history_df is None else history_df\n", "    F = assemble_features(df)\n", "    labels = label_states(F)\n", "    clf = train_state_classifier(F, labels)\n", "  \n", "    # Predict states and produce simple signal: in attractor (1) trend-follow; in transition (2) momentum; in noise (0) flat\n", "    X = F.select_dtypes(include=[np.number]).fillna(0)\n", "    states = pd.Series(clf.predict(X), index=F.index, name='state')\n", "\n", "    # Build signals\n", "    signals = pd.Series(0.0, index=F.index)\n", "    # Attractor: mean-revert small positions\n", "    signals[states == 1] = -0.5 * np.sign(F['returns'][states == 1].rolling(3).mean().fillna(0))\n", "    # Transition: trend follow\n", "    signals[states == 2] = 1.0 * np.sign(F['returns'][states == 2].rolling(5).mean().fillna(0))\n", "    # Noise: 0\n", "\n", "    # Backtest on base and multiverse\n", "    base_bt = simple_vectorized_backtest(df, signals)\n", "\n", "    multiverse_results = []\n", "    variants = multiverse_warp(df, n_variants=6)\n", "    for vdf in variants:\n", "        try:\n", "            VF = assemble_features(vdf)\n", "            VX = VF.select_dtypes(include=[np.number]).fillna(0)\n", "            vstates = pd.Series(clf.predict(VX), index=VF.index)\n", "            vsignals = pd.Series(0.0, index=VF.index)\n", "            vsignals[vstates == 1] = -0.5 * np.sign(VF['returns'][vstates == 1].rolling(3).mean().fillna(0))\n", "            vsignals[vstates == 2] = 1.0 * np.sign(VF['returns'][vstates == 2].rolling(5).mean().fillna(0))\n", "            vbt = simple_vectorized_backtest(vdf, vsignals)\n", "            multiverse_results.append(vbt)\n", "        except Exception as e:\n", "            print(\"<PERSON><PERSON><PERSON> failed:\", e)\n", "\n", "    # Adversarial injection test\n", "    adv = inject_adversarial_events(df, n_events=4)\n", "    AF = assemble_features(adv)\n", "    AX = AF.select_dtypes(include=[np.number]).fillna(0)\n", "    astates = pd.Series(clf.predict(AX), index=AF.index)\n", "    asignals = pd.Series(0.0, index=AF.index)\n", "    asignals[astates == 1] = -0.5 * np.sign(AF['returns'][astates == 1].rolling(3).mean().fillna(0))\n", "    asignals[astates == 2] = 1.0 * np.sign(AF['returns'][astates == 2].rolling(5).mean().fillna(0))\n", "    adv_bt = simple_vectorized_backtest(adv, asignals)\n", "\n", "    # Metrics\n", "    metrics = {\n", "        'base_PADDR': profit_adjusted_drawdown_ratio(base_bt['equity']),\n", "        'adv_PADDR': profit_adjusted_drawdown_ratio(adv_bt['equity']),\n", "        'multiverse_count': len(multiverse_results)\n", "    }\n", "\n", "    artifacts = {\n", "        'classifier': clf,\n", "        'features_snapshot': F,\n", "        'labels_snapshot': labels,\n", "        'base_backtest': base_bt,\n", "        'multiverse_backtests': multiverse_results,\n", "        'adversarial_backtest': adv_bt,\n", "        'metrics': metrics\n", "    }\n", "    return artifacts"]}, {"cell_type": "markdown", "id": "e3ae84e1", "metadata": {}, "source": ["### Test Framework"]}, {"cell_type": "code", "execution_count": 11, "id": "1e1e686a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found in-memory history_df; using it.\n"]}], "source": ["hist = None\n", "try:\n", "    hist = load_history_df(\"\")\n", "except Exception as e:\n", "    print('Loader error:', e)\n", "    raise"]}, {"cell_type": "code", "execution_count": 12, "id": "bad278d7", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'returns'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/pandas/core/indexes/base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3805\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'returns'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m art \u001b[38;5;241m=\u001b[39m \u001b[43mbuild_and_backtest_pipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhistory_df\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhist\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[10], line 224\u001b[0m, in \u001b[0;36mbuild_and_backtest_pipeline\u001b[0;34m(history_df, csv_path)\u001b[0m\n\u001b[1;32m    222\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mbuild_and_backtest_pipeline\u001b[39m(history_df\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, csv_path\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    223\u001b[0m     df \u001b[38;5;241m=\u001b[39m load_history_df(csv_path) \u001b[38;5;28;01mif\u001b[39;00m history_df \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m history_df\n\u001b[0;32m--> 224\u001b[0m     F \u001b[38;5;241m=\u001b[39m \u001b[43massemble_features\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    225\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mClassifier trained.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    226\u001b[0m     labels \u001b[38;5;241m=\u001b[39m label_states(F)\n", "Cell \u001b[0;32mIn[10], line 73\u001b[0m, in \u001b[0;36massemble_features\u001b[0;34m(df)\u001b[0m\n\u001b[1;32m     71\u001b[0m f[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mclose\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mclose\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m     72\u001b[0m f[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mreturns\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mclose\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mpct_change()\u001b[38;5;241m.\u001b[39mfillna(\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m---> 73\u001b[0m f[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvolatility\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mreturns\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mrolling(\u001b[38;5;241m21\u001b[39m)\u001b[38;5;241m.\u001b[39mstd()\n\u001b[1;32m     74\u001b[0m f[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwick_imbalance\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m wick_imbalance(df)\n\u001b[1;32m     75\u001b[0m ent \u001b[38;5;241m=\u001b[39m rolling_entropy_features(df, window\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m64\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/pandas/core/frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[0;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[1;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/pandas/core/indexes/base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[1;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[1;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[1;32m   3810\u001b[0m     ):\n\u001b[1;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[0;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[1;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[1;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[1;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[1;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[1;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'returns'"]}], "source": ["art = build_and_backtest_pipeline(history_df=hist)"]}, {"cell_type": "code", "execution_count": null, "id": "130f2911", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "hedgehog-backend", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}